using System;
using System.Drawing;
using System.Windows.Forms;
using Aspose.Slides;
using Aspose.Slides.Theme;
using System.Linq;
using System.Collections.Generic;

namespace PPTPiliangChuli.Forms
{
    /// <summary>
    /// PPT全局格式设置窗体
    /// </summary>
    public partial class PPTFormatSettingsForm : Form
    {
        #region 私有字段

        /// <summary>
        /// 当前设置是否已修改
        /// </summary>
        private bool _isModified = false;

        #region 段落格式控件
        private RadioButton? _rbAlignLeft, _rbAlignCenter, _rbAlignRight, _rbAlignJustify, _rbAlignDistribute;
        private NumericUpDown? _numIndentBefore, _numIndentSpecial;
        private ComboBox? _cmbIndentSpecial;
        private NumericUpDown? _numSpacingBefore, _numSpacingAfter, _numLineSpacing;
        private ComboBox? _cmbLineSpacing;
        private CheckBox? _chkChineseControl, _chkWordWrap, _chkPunctuationBoundary;
        private RadioButton? _rbTextAlignAuto, _rbTextAlignCenter, _rbTextAlignBaseline, _rbTextAlignBottom;
        #endregion

        #region 字体格式控件
        private ComboBox? _cmbChineseFont, _cmbEnglishFont, _cmbFontStyle, _cmbUnderline;
        private NumericUpDown? _numFontSize;
        private Button? _btnFontColor, _btnUnderlineColor;
        private CheckBox? _chkStrikethrough, _chkDoubleStrikethrough, _chkSuperscript, _chkSubscript;
        private Color _fontColor = Color.Black;
        private Color _underlineColor = Color.Black;
        #endregion

        #region 主题设置控件
        private ListBox? _listThemes;
        private ComboBox? _cmbColorScheme, _cmbFontScheme;
        private Panel? _panelColorPreview;
        private Label? _lblFontPreview;
        #endregion

        #region 母版设置控件
        private Button? _btnSlideMaster, _btnTitleMaster, _btnNotesMaster, _btnHandoutMaster;
        #endregion

        #region 布局设置控件
        private Button? _btnTitleLayout, _btnContentLayout, _btnTwoColumnLayout, _btnPictureLayout, _btnCustomLayout;
        #endregion

        #region 样式设置控件
        private Button? _btnShapeStyle, _btnTextStyle, _btnTableStyle, _btnChartStyle;
        #endregion

        #region 格式设置数据
        /// <summary>
        /// 当前演示文稿对象（用于预览和应用设置）
        /// </summary>
        private Presentation? _currentPresentation;

        /// <summary>
        /// 段落格式设置
        /// </summary>
        public ParagraphFormatSettings ParagraphFormat { get; set; } = new ParagraphFormatSettings();

        /// <summary>
        /// 字体格式设置
        /// </summary>
        public FontFormatSettings FontFormat { get; set; } = new FontFormatSettings();

        /// <summary>
        /// 主题设置
        /// </summary>
        public ThemeSettings ThemeSettings { get; set; } = new ThemeSettings();
        #endregion

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化PPT格式设置窗体
        /// </summary>
        public PPTFormatSettingsForm()
        {
            InitializeComponent();
            InitializeCustomControls();
            LoadCurrentSettings();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化自定义控件
        /// </summary>
        private void InitializeCustomControls()
        {
            try
            {
                // 设置窗体属性
                SetupFormProperties();

                // 初始化标签页内容
                InitializeParagraphTab();
                InitializeFontTab();
                InitializeThemeTab();
                InitializeMasterTab();
                InitializeLayoutTab();
                InitializeStyleTab();

                // 设置事件处理器
                SetupEventHandlers();

                // 设置标签页宽度均匀分布
                SetupTabWidths();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化控件时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 设置窗体属性
        /// </summary>
        private void SetupFormProperties()
        {
            // 设置窗体图标和标题
            this.Text = "PPT全局格式设置";
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowInTaskbar = false;
            this.StartPosition = FormStartPosition.CenterParent;

            // 设置窗体大小
            this.Size = new Size(900, 650);
            this.MinimumSize = new Size(900, 650);
        }

        /// <summary>
        /// 设置标签页宽度均匀分布
        /// </summary>
        private void SetupTabWidths()
        {
            try
            {
                // 计算每个标签页的宽度
                int tabCount = tabControlMain.TabPages.Count;
                if (tabCount > 0)
                {
                    int totalWidth = tabControlMain.Width;
                    int tabWidth = totalWidth / tabCount;

                    // 设置标签页宽度
                    tabControlMain.SizeMode = TabSizeMode.Fixed;
                    tabControlMain.ItemSize = new Size(tabWidth - 2, tabControlMain.ItemSize.Height);
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不影响程序运行
                Console.WriteLine($"设置标签页宽度时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            // 按钮事件
            btnOK.Click += BtnOK_Click;
            btnCancel.Click += BtnCancel_Click;
            btnApply.Click += BtnApply_Click;
            btnReset.Click += BtnReset_Click;

            // 窗体事件
            this.FormClosing += PPTFormatSettingsForm_FormClosing;
            this.Resize += PPTFormatSettingsForm_Resize;
        }

        #endregion

        #region 标签页初始化方法

        /// <summary>
        /// 初始化段落格式设置标签页
        /// </summary>
        private void InitializeParagraphTab()
        {
            try
            {
                CreateParagraphAlignmentGroup();
                CreateParagraphIndentGroup();
                CreateParagraphSpacingGroup();
                CreateParagraphOptionsGroup();
                CreateTextAlignmentGroup();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化段落格式设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 初始化字体格式设置标签页
        /// </summary>
        private void InitializeFontTab()
        {
            try
            {
                CreateFontSelectionGroup();
                CreateFontStyleGroup();
                CreateFontEffectsGroup();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化字体格式设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 初始化主题设置标签页
        /// </summary>
        private void InitializeThemeTab()
        {
            try
            {
                CreateBuiltInThemeGroup();
                CreateCustomThemeGroup();
                CreateThemeColorSchemeGroup();
                CreateThemeFontSchemeGroup();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化主题设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 初始化母版设置标签页
        /// </summary>
        private void InitializeMasterTab()
        {
            CreateMasterSlideGroup();
        }

        /// <summary>
        /// 初始化布局设置标签页
        /// </summary>
        private void InitializeLayoutTab()
        {
            CreateSlideLayoutGroup();
        }

        /// <summary>
        /// 初始化样式设置标签页
        /// </summary>
        private void InitializeStyleTab()
        {
            CreateStyleGroup();
        }

        #endregion

        #region 事件处理方法

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object? sender, EventArgs e)
        {
            try
            {
                if (ApplySettings())
                {
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            if (CheckForUnsavedChanges())
            {
                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
        }

        /// <summary>
        /// 应用按钮点击事件
        /// </summary>
        private void BtnApply_Click(object? sender, EventArgs e)
        {
            try
            {
                ApplySettings();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void BtnReset_Click(object? sender, EventArgs e)
        {
            var result = MessageBox.Show("确定要重置所有设置为默认值吗？", "确认重置",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                ResetToDefaults();
            }
        }

        /// <summary>
        /// 窗体关闭事件
        /// </summary>
        private void PPTFormatSettingsForm_FormClosing(object? sender, FormClosingEventArgs e)
        {
            if (this.DialogResult != DialogResult.OK && !CheckForUnsavedChanges())
            {
                e.Cancel = true;
            }
        }

        /// <summary>
        /// 窗体大小改变事件
        /// </summary>
        private void PPTFormatSettingsForm_Resize(object? sender, EventArgs e)
        {
            SetupTabWidths();
        }

        /// <summary>
        /// 幻灯片母版按钮点击事件
        /// </summary>
        private void BtnSlideMaster_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("幻灯片母版功能允许您设置普通幻灯片的整体布局和格式。\n\n" +
                               "功能包括：\n" +
                               "• 设置默认字体和颜色\n" +
                               "• 定义占位符位置和样式\n" +
                               "• 配置背景和主题元素\n" +
                               "• 设置页眉页脚格式\n\n" +
                               "此功能需要在实际PPT处理时实现具体逻辑。", "幻灯片母版设置",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                _isModified = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开幻灯片母版设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 标题母版按钮点击事件
        /// </summary>
        private void BtnTitleMaster_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("标题母版功能允许您设置标题幻灯片的专用布局和格式。\n\n" +
                               "功能包括：\n" +
                               "• 设置标题和副标题样式\n" +
                               "• 定义标题页面布局\n" +
                               "• 配置标题页背景\n" +
                               "• 设置标题页特殊元素\n\n" +
                               "此功能需要在实际PPT处理时实现具体逻辑。", "标题母版设置",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                _isModified = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开标题母版设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 备注母版按钮点击事件
        /// </summary>
        private void BtnNotesMaster_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("备注母版功能允许您设置备注页面的布局和格式。\n\n" +
                               "功能包括：\n" +
                               "• 设置备注文本格式\n" +
                               "• 定义备注页面布局\n" +
                               "• 配置幻灯片缩略图位置\n" +
                               "• 设置页眉页脚信息\n\n" +
                               "此功能需要在实际PPT处理时实现具体逻辑。", "备注母版设置",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                _isModified = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开备注母版设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 讲义母版按钮点击事件
        /// </summary>
        private void BtnHandoutMaster_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("讲义母版功能允许您设置打印讲义的布局和格式。\n\n" +
                               "功能包括：\n" +
                               "• 设置每页幻灯片数量\n" +
                               "• 定义幻灯片排列方式\n" +
                               "• 配置页面边距和间距\n" +
                               "• 设置页眉页脚信息\n\n" +
                               "此功能需要在实际PPT处理时实现具体逻辑。", "讲义母版设置",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                _isModified = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开讲义母版设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 标题幻灯片布局按钮点击事件
        /// </summary>
        private void BtnTitleLayout_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("标题幻灯片布局功能允许您设置标题幻灯片的专用布局。\n\n" +
                               "功能包括：\n" +
                               "• 设置标题和副标题位置\n" +
                               "• 定义标题文本格式\n" +
                               "• 配置标题页面元素\n" +
                               "• 设置标题页背景样式\n\n" +
                               "此功能需要在实际PPT处理时实现具体逻辑。", "标题幻灯片布局",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                _isModified = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置标题幻灯片布局时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 内容布局按钮点击事件
        /// </summary>
        private void BtnContentLayout_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("内容布局功能允许您设置标准内容幻灯片的布局。\n\n" +
                               "功能包括：\n" +
                               "• 设置标题和内容区域位置\n" +
                               "• 定义内容占位符样式\n" +
                               "• 配置文本和图片区域\n" +
                               "• 设置内容排列方式\n\n" +
                               "此功能需要在实际PPT处理时实现具体逻辑。", "内容布局设置",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                _isModified = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置内容布局时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 两栏布局按钮点击事件
        /// </summary>
        private void BtnTwoColumnLayout_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("两栏布局功能允许您设置双栏内容的幻灯片布局。\n\n" +
                               "功能包括：\n" +
                               "• 设置左右两栏的宽度比例\n" +
                               "• 定义栏间距和边距\n" +
                               "• 配置每栏的内容类型\n" +
                               "• 设置栏标题和内容格式\n\n" +
                               "此功能需要在实际PPT处理时实现具体逻辑。", "两栏布局设置",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                _isModified = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置两栏布局时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 图片布局按钮点击事件
        /// </summary>
        private void BtnPictureLayout_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("图片布局功能允许您设置以图片为主的幻灯片布局。\n\n" +
                               "功能包括：\n" +
                               "• 设置图片显示区域大小\n" +
                               "• 定义图片标题和说明位置\n" +
                               "• 配置图片排列方式\n" +
                               "• 设置图片边框和效果\n\n" +
                               "此功能需要在实际PPT处理时实现具体逻辑。", "图片布局设置",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                _isModified = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置图片布局时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 自定义布局按钮点击事件
        /// </summary>
        private void BtnCustomLayout_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("自定义布局功能允许您创建和编辑个性化的幻灯片布局。\n\n" +
                               "功能包括：\n" +
                               "• 自由设置占位符位置和大小\n" +
                               "• 定义自定义元素和样式\n" +
                               "• 配置特殊布局需求\n" +
                               "• 保存和重用自定义布局\n\n" +
                               "此功能需要在实际PPT处理时实现具体逻辑。", "自定义布局设置",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                _isModified = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置自定义布局时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 形状样式按钮点击事件
        /// </summary>
        private void BtnShapeStyle_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("形状样式功能允许您设置各种形状的外观样式。\n\n" +
                               "功能包括：\n" +
                               "• 设置形状填充颜色和效果\n" +
                               "• 定义形状边框样式和颜色\n" +
                               "• 配置形状阴影和3D效果\n" +
                               "• 设置形状透明度和渐变\n\n" +
                               "此功能需要在实际PPT处理时实现具体逻辑。", "形状样式设置",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                _isModified = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置形状样式时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 文本样式按钮点击事件
        /// </summary>
        private void BtnTextStyle_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("文本样式功能允许您设置文本的外观样式。\n\n" +
                               "功能包括：\n" +
                               "• 设置文本字体和大小\n" +
                               "• 定义文本颜色和效果\n" +
                               "• 配置文本对齐和间距\n" +
                               "• 设置文本装饰和特效\n\n" +
                               "此功能需要在实际PPT处理时实现具体逻辑。", "文本样式设置",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                _isModified = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置文本样式时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 表格样式按钮点击事件
        /// </summary>
        private void BtnTableStyle_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("表格样式功能允许您设置表格的外观样式。\n\n" +
                               "功能包括：\n" +
                               "• 设置表格边框样式和颜色\n" +
                               "• 定义单元格填充和间距\n" +
                               "• 配置表头和数据行样式\n" +
                               "• 设置表格整体效果和主题\n\n" +
                               "此功能需要在实际PPT处理时实现具体逻辑。", "表格样式设置",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                _isModified = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置表格样式时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 图表样式按钮点击事件
        /// </summary>
        private void BtnChartStyle_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("图表样式功能允许您设置图表的外观样式。\n\n" +
                               "功能包括：\n" +
                               "• 设置图表颜色主题和配色\n" +
                               "• 定义图表字体和标签样式\n" +
                               "• 配置图表边框和背景\n" +
                               "• 设置图表效果和动画\n\n" +
                               "此功能需要在实际PPT处理时实现具体逻辑。", "图表样式设置",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                _isModified = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置图表样式时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 控件值变更事件处理器
        /// </summary>
        private void OnControlValueChanged(object? sender, EventArgs e)
        {
            _isModified = true;
        }

        /// <summary>
        /// 获取对比色（用于按钮文字颜色）
        /// </summary>
        /// <param name="color">背景颜色</param>
        /// <returns>对比色</returns>
        private static Color GetContrastColor(Color color)
        {
            // 计算亮度
            double brightness = (color.R * 0.299 + color.G * 0.587 + color.B * 0.114) / 255;
            return brightness > 0.5 ? Color.Black : Color.White;
        }

        /// <summary>
        /// 字体颜色按钮点击事件
        /// </summary>
        private void BtnFontColor_Click(object? sender, EventArgs e)
        {
            using var colorDialog = new ColorDialog
            {
                Color = _fontColor,
                FullOpen = true
            };

            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                _fontColor = colorDialog.Color;
                if (_btnFontColor != null)
                {
                    _btnFontColor.BackColor = _fontColor;
                    _btnFontColor.ForeColor = GetContrastColor(_fontColor);
                }
                _isModified = true;
            }
        }

        /// <summary>
        /// 下划线颜色按钮点击事件
        /// </summary>
        private void BtnUnderlineColor_Click(object? sender, EventArgs e)
        {
            using var colorDialog = new ColorDialog
            {
                Color = _underlineColor,
                FullOpen = true
            };

            if (colorDialog.ShowDialog(this) == DialogResult.OK)
            {
                _underlineColor = colorDialog.Color;
                if (_btnUnderlineColor != null)
                {
                    _btnUnderlineColor.BackColor = _underlineColor;
                    _btnUnderlineColor.ForeColor = GetContrastColor(_underlineColor);
                }
                _isModified = true;
            }
        }

        /// <summary>
        /// 应用主题按钮点击事件
        /// </summary>
        private void BtnApplyTheme_Click(object? sender, EventArgs e)
        {
            try
            {
                if (_listThemes?.SelectedItem != null)
                {
                    string selectedTheme = _listThemes.SelectedItem.ToString() ?? "";

                    // 应用选中的主题
                    ApplySelectedTheme(selectedTheme);

                    MessageBox.Show($"主题 '{selectedTheme}' 已成功应用！\n\n" +
                                   "主题设置将在处理PPT文件时生效。", "主题应用成功",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    _isModified = true;
                }
                else
                {
                    MessageBox.Show("请先选择一个主题。", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用主题时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 预览主题按钮点击事件
        /// </summary>
        private void BtnPreviewTheme_Click(object? sender, EventArgs e)
        {
            try
            {
                if (_listThemes?.SelectedItem != null)
                {
                    string selectedTheme = _listThemes.SelectedItem.ToString() ?? "";
                    // 这里将在后续实现具体的主题预览逻辑
                    MessageBox.Show($"预览主题: {selectedTheme}\n\n此功能将在后续版本中实现。", "信息",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"预览主题时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 应用颜色方案按钮点击事件
        /// </summary>
        private void BtnApplyColorScheme_Click(object? sender, EventArgs e)
        {
            try
            {
                if (_cmbColorScheme?.SelectedItem != null)
                {
                    string selectedScheme = _cmbColorScheme.SelectedItem.ToString() ?? "";
                    // 这里将在后续实现具体的颜色方案应用逻辑
                    MessageBox.Show($"应用颜色方案: {selectedScheme}\n\n此功能将在后续版本中实现。", "信息",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    _isModified = true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用颜色方案时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 自定义颜色方案按钮点击事件
        /// </summary>
        private void BtnCustomColorScheme_Click(object? sender, EventArgs e)
        {
            try
            {
                // 这里将在后续实现具体的自定义颜色方案逻辑
                MessageBox.Show("自定义颜色方案功能将在后续版本中实现。", "信息",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开自定义颜色方案时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 应用字体方案按钮点击事件
        /// </summary>
        private void BtnApplyFontScheme_Click(object? sender, EventArgs e)
        {
            try
            {
                if (_cmbFontScheme?.SelectedItem != null)
                {
                    string selectedScheme = _cmbFontScheme.SelectedItem.ToString() ?? "";
                    // 这里将在后续实现具体的字体方案应用逻辑
                    MessageBox.Show($"应用字体方案: {selectedScheme}\n\n此功能将在后续版本中实现。", "信息",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // 更新字体预览
                    if (_lblFontPreview != null)
                    {
                        _lblFontPreview.Text = $"标题字体: {GetTitleFontForScheme(selectedScheme)}\n正文字体: {GetBodyFontForScheme(selectedScheme)}";
                    }
                    _isModified = true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用字体方案时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 自定义字体方案按钮点击事件
        /// </summary>
        private void BtnCustomFontScheme_Click(object? sender, EventArgs e)
        {
            try
            {
                // 这里将在后续实现具体的自定义字体方案逻辑
                MessageBox.Show("自定义字体方案功能将在后续版本中实现。", "信息",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开自定义字体方案时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 根据方案获取标题字体
        /// </summary>
        private static string GetTitleFontForScheme(string scheme)
        {
            return scheme switch
            {
                "Office 字体方案" => "微软雅黑",
                "经典字体方案" => "宋体",
                "现代字体方案" => "Calibri",
                "优雅字体方案" => "华文楷体",
                "简洁字体方案" => "Arial",
                "艺术字体方案" => "华文行楷",
                _ => "微软雅黑"
            };
        }

        /// <summary>
        /// 根据方案获取正文字体
        /// </summary>
        private static string GetBodyFontForScheme(string scheme)
        {
            return scheme switch
            {
                "Office 字体方案" => "宋体",
                "经典字体方案" => "仿宋",
                "现代字体方案" => "Verdana",
                "优雅字体方案" => "华文宋体",
                "简洁字体方案" => "Tahoma",
                "艺术字体方案" => "华文中宋",
                _ => "宋体"
            };
        }

        /// <summary>
        /// 加载当前设置
        /// </summary>
        private void LoadCurrentSettings()
        {
            try
            {
                // 加载当前的PPT格式设置
                // 这里将在后续实现具体的设置加载逻辑
                _isModified = false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 应用设置
        /// </summary>
        /// <returns>是否成功应用</returns>
        private bool ApplySettings()
        {
            try
            {
                // 收集当前UI设置到数据对象
                CollectUISettings();

                // 应用PPT格式设置
                ApplyParagraphFormatSettings();
                ApplyFontFormatSettings();
                ApplyThemeSettings();

                _isModified = false;
                MessageBox.Show("格式设置已成功应用！", "成功",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用设置失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 重置为默认设置
        /// </summary>
        private void ResetToDefaults()
        {
            try
            {
                // 重置所有设置为默认值
                // 这里将在后续实现具体的重置逻辑
                _isModified = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"重置设置时发生错误: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 检查未保存的更改
        /// </summary>
        /// <returns>是否可以继续操作</returns>
        private bool CheckForUnsavedChanges()
        {
            if (_isModified)
            {
                var result = MessageBox.Show("设置已修改但未保存，确定要放弃更改吗？", "确认",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                return result == DialogResult.Yes;
            }
            return true;
        }

        /// <summary>
        /// 设置ComboBox文字居中显示
        /// </summary>
        /// <param name="comboBox">要设置的ComboBox</param>
        private static void SetComboBoxTextAlign(ComboBox comboBox)
        {
            comboBox.DrawMode = DrawMode.OwnerDrawFixed;
            comboBox.DrawItem += (sender, e) =>
            {
                if (e.Index < 0) return;

                e.DrawBackground();

                var text = comboBox.Items[e.Index].ToString();
                if (!string.IsNullOrEmpty(text))
                {
                    var textBounds = e.Bounds;
                    var textFlags = TextFormatFlags.HorizontalCenter | TextFormatFlags.VerticalCenter;
                    TextRenderer.DrawText(e.Graphics, text, e.Font, textBounds, e.ForeColor, textFlags);
                }

                e.DrawFocusRectangle();
            };
        }

        /// <summary>
        /// 设置NumericUpDown文字居中显示
        /// </summary>
        /// <param name="numericUpDown">要设置的NumericUpDown</param>
        private static void SetNumericUpDownTextAlign(NumericUpDown numericUpDown)
        {
            numericUpDown.TextAlign = HorizontalAlignment.Center;
        }

        /// <summary>
        /// 收集UI设置到数据对象
        /// </summary>
        private void CollectUISettings()
        {
            // 收集段落格式设置
            if (_rbAlignLeft?.Checked == true) ParagraphFormat.Alignment = TextAlignment.Left;
            else if (_rbAlignCenter?.Checked == true) ParagraphFormat.Alignment = TextAlignment.Center;
            else if (_rbAlignRight?.Checked == true) ParagraphFormat.Alignment = TextAlignment.Right;
            else if (_rbAlignJustify?.Checked == true) ParagraphFormat.Alignment = TextAlignment.Justify;
            else if (_rbAlignDistribute?.Checked == true) ParagraphFormat.Alignment = TextAlignment.Distributed;

            ParagraphFormat.IndentBefore = (float)(_numIndentBefore?.Value ?? 0);
            ParagraphFormat.SpecialIndentType = _cmbIndentSpecial?.SelectedItem?.ToString() ?? "无";
            ParagraphFormat.SpecialIndentValue = (float)(_numIndentSpecial?.Value ?? 2);
            ParagraphFormat.SpacingBefore = (float)(_numSpacingBefore?.Value ?? 0);
            ParagraphFormat.SpacingAfter = (float)(_numSpacingAfter?.Value ?? 0);
            ParagraphFormat.LineSpacingType = _cmbLineSpacing?.SelectedItem?.ToString() ?? "单倍行距";
            ParagraphFormat.LineSpacingValue = (float)(_numLineSpacing?.Value ?? 1);
            ParagraphFormat.ChineseControl = _chkChineseControl?.Checked ?? true;
            ParagraphFormat.WordWrap = _chkWordWrap?.Checked ?? false;
            ParagraphFormat.PunctuationBoundary = _chkPunctuationBoundary?.Checked ?? false;

            if (_rbTextAlignAuto?.Checked == true) ParagraphFormat.TextAlignmentType = "自动";
            else if (_rbTextAlignCenter?.Checked == true) ParagraphFormat.TextAlignmentType = "居中";
            else if (_rbTextAlignBaseline?.Checked == true) ParagraphFormat.TextAlignmentType = "基线";
            else if (_rbTextAlignBottom?.Checked == true) ParagraphFormat.TextAlignmentType = "底部";

            // 收集字体格式设置
            FontFormat.ChineseFont = _cmbChineseFont?.SelectedItem?.ToString() ?? "宋体";
            FontFormat.EnglishFont = _cmbEnglishFont?.SelectedItem?.ToString() ?? "Arial";
            FontFormat.FontStyle = _cmbFontStyle?.SelectedItem?.ToString() ?? "常规";
            FontFormat.FontSize = (float)(_numFontSize?.Value ?? 12);
            FontFormat.FontColor = _fontColor;
            FontFormat.UnderlineType = _cmbUnderline?.SelectedItem?.ToString() ?? "无";
            FontFormat.UnderlineColor = _underlineColor;
            FontFormat.Strikethrough = _chkStrikethrough?.Checked ?? false;
            FontFormat.DoubleStrikethrough = _chkDoubleStrikethrough?.Checked ?? false;
            FontFormat.Superscript = _chkSuperscript?.Checked ?? false;
            FontFormat.Subscript = _chkSubscript?.Checked ?? false;

            // 收集主题设置
            ThemeSettings.SelectedTheme = _listThemes?.SelectedItem?.ToString() ?? "Office 主题";
            ThemeSettings.SelectedColorScheme = _cmbColorScheme?.SelectedItem?.ToString() ?? "Office 颜色方案";
            ThemeSettings.SelectedFontScheme = _cmbFontScheme?.SelectedItem?.ToString() ?? "Office 字体方案";
        }

        /// <summary>
        /// 应用段落格式设置
        /// </summary>
        private void ApplyParagraphFormatSettings()
        {
            // 这里实现段落格式的实际应用逻辑
            // 由于需要具体的PPT文件，这里只是示例实现
            // 在实际使用时，会传入Presentation对象进行格式应用
        }

        /// <summary>
        /// 应用字体格式设置
        /// </summary>
        private void ApplyFontFormatSettings()
        {
            // 这里实现字体格式的实际应用逻辑
            // 由于需要具体的PPT文件，这里只是示例实现
            // 在实际使用时，会传入Presentation对象进行格式应用
        }

        /// <summary>
        /// 应用主题设置
        /// </summary>
        private void ApplyThemeSettings()
        {
            // 这里实现主题设置的实际应用逻辑
            // 由于需要具体的PPT文件，这里只是示例实现
            // 在实际使用时，会传入Presentation对象进行格式应用
        }

        /// <summary>
        /// 应用选中的主题
        /// </summary>
        /// <param name="themeName">主题名称</param>
        private void ApplySelectedTheme(string themeName)
        {
            try
            {
                // 更新主题设置数据
                ThemeSettings.SelectedTheme = themeName;

                // 根据主题名称设置相应的颜色和字体方案
                switch (themeName)
                {
                    case "Office 主题":
                        ThemeSettings.SelectedColorScheme = "Office 颜色方案";
                        ThemeSettings.SelectedFontScheme = "Office 字体方案";
                        break;
                    case "Facet 主题":
                        ThemeSettings.SelectedColorScheme = "蓝色方案";
                        ThemeSettings.SelectedFontScheme = "现代字体方案";
                        break;
                    case "Ion 主题":
                        ThemeSettings.SelectedColorScheme = "绿色方案";
                        ThemeSettings.SelectedFontScheme = "简洁字体方案";
                        break;
                    case "Retrospect 主题":
                        ThemeSettings.SelectedColorScheme = "橙色方案";
                        ThemeSettings.SelectedFontScheme = "经典字体方案";
                        break;
                    case "Slice 主题":
                        ThemeSettings.SelectedColorScheme = "红色方案";
                        ThemeSettings.SelectedFontScheme = "优雅字体方案";
                        break;
                    case "Wisp 主题":
                        ThemeSettings.SelectedColorScheme = "紫色方案";
                        ThemeSettings.SelectedFontScheme = "艺术字体方案";
                        break;
                    default:
                        ThemeSettings.SelectedColorScheme = "Office 颜色方案";
                        ThemeSettings.SelectedFontScheme = "Office 字体方案";
                        break;
                }

                // 更新UI显示
                UpdateThemeUI();
            }
            catch (Exception ex)
            {
                throw new Exception($"应用主题 '{themeName}' 时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新主题相关的UI显示
        /// </summary>
        private void UpdateThemeUI()
        {
            try
            {
                // 更新颜色方案下拉框
                if (_cmbColorScheme != null)
                {
                    var colorSchemeIndex = _cmbColorScheme.Items.IndexOf(ThemeSettings.SelectedColorScheme);
                    if (colorSchemeIndex >= 0)
                    {
                        _cmbColorScheme.SelectedIndex = colorSchemeIndex;
                    }
                }

                // 更新字体方案下拉框
                if (_cmbFontScheme != null)
                {
                    var fontSchemeIndex = _cmbFontScheme.Items.IndexOf(ThemeSettings.SelectedFontScheme);
                    if (fontSchemeIndex >= 0)
                    {
                        _cmbFontScheme.SelectedIndex = fontSchemeIndex;
                    }
                }

                // 更新字体预览
                if (_lblFontPreview != null)
                {
                    _lblFontPreview.Text = $"标题字体: {GetTitleFontForScheme(ThemeSettings.SelectedFontScheme)}\n正文字体: {GetBodyFontForScheme(ThemeSettings.SelectedFontScheme)}";
                }

                // 更新颜色预览
                UpdateColorPreview();
            }
            catch (Exception ex)
            {
                // 记录错误但不影响主要功能
                Console.WriteLine($"更新主题UI时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新颜色预览
        /// </summary>
        private void UpdateColorPreview()
        {
            try
            {
                if (_panelColorPreview?.Controls != null)
                {
                    for (int i = 0; i < _panelColorPreview.Controls.Count && i < 8; i++)
                    {
                        if (_panelColorPreview.Controls[i] is Panel colorBlock)
                        {
                            colorBlock.BackColor = GetSchemeColor(ThemeSettings.SelectedColorScheme, i);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不影响主要功能
                Console.WriteLine($"更新颜色预览时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据颜色方案和索引获取颜色
        /// </summary>
        /// <param name="colorScheme">颜色方案名称</param>
        /// <param name="index">颜色索引</param>
        /// <returns>颜色</returns>
        private static Color GetSchemeColor(string colorScheme, int index)
        {
            var colors = colorScheme switch
            {
                "蓝色方案" => new Color[] {
                    Color.FromArgb(68, 114, 196), Color.FromArgb(91, 155, 213), Color.FromArgb(142, 169, 219),
                    Color.FromArgb(189, 215, 238), Color.FromArgb(68, 84, 106), Color.FromArgb(47, 117, 181),
                    Color.FromArgb(31, 78, 120), Color.FromArgb(20, 57, 92)
                },
                "绿色方案" => new Color[] {
                    Color.FromArgb(112, 173, 71), Color.FromArgb(146, 208, 80), Color.FromArgb(169, 208, 142),
                    Color.FromArgb(196, 215, 155), Color.FromArgb(84, 130, 53), Color.FromArgb(118, 147, 60),
                    Color.FromArgb(79, 129, 189), Color.FromArgb(54, 96, 146)
                },
                "橙色方案" => new Color[] {
                    Color.FromArgb(237, 125, 49), Color.FromArgb(255, 192, 0), Color.FromArgb(248, 203, 173),
                    Color.FromArgb(252, 213, 180), Color.FromArgb(198, 89, 17), Color.FromArgb(244, 176, 132),
                    Color.FromArgb(227, 108, 9), Color.FromArgb(166, 77, 7)
                },
                _ => new Color[] {
                    Color.FromArgb(68, 114, 196), Color.FromArgb(237, 125, 49), Color.FromArgb(165, 165, 165),
                    Color.FromArgb(255, 192, 0), Color.FromArgb(91, 155, 213), Color.FromArgb(112, 173, 71),
                    Color.FromArgb(158, 72, 14), Color.FromArgb(99, 99, 99)
                }
            };
            return index < colors.Length ? colors[index] : Color.Gray;
        }

        /// <summary>
        /// 根据字体方案获取标题字体
        /// </summary>
        /// <param name="fontScheme">字体方案名称</param>
        /// <returns>标题字体名称</returns>
        private static string GetTitleFontForScheme(string fontScheme)
        {
            return fontScheme switch
            {
                "现代字体方案" => "Calibri Light",
                "简洁字体方案" => "Arial",
                "经典字体方案" => "Times New Roman",
                "优雅字体方案" => "Georgia",
                "艺术字体方案" => "Trebuchet MS",
                _ => "Calibri"
            };
        }

        /// <summary>
        /// 根据字体方案获取正文字体
        /// </summary>
        /// <param name="fontScheme">字体方案名称</param>
        /// <returns>正文字体名称</returns>
        private static string GetBodyFontForScheme(string fontScheme)
        {
            return fontScheme switch
            {
                "现代字体方案" => "Calibri",
                "简洁字体方案" => "Arial",
                "经典字体方案" => "Times New Roman",
                "优雅字体方案" => "Georgia",
                "艺术字体方案" => "Trebuchet MS",
                _ => "Calibri"
            };
        }

        #endregion

        #region 段落格式设置控件创建方法

        /// <summary>
        /// 创建段落对齐方式组
        /// </summary>
        private void CreateParagraphAlignmentGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "对齐方式",
                Location = new Point(20, 20),
                Size = new Size(200, 120),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 左对齐
            _rbAlignLeft = new RadioButton
            {
                Text = "左对齐(&L)",
                Location = new Point(15, 25),
                Size = new Size(80, 20),
                Checked = true,
                Tag = "Left"
            };
            _rbAlignLeft.CheckedChanged += OnControlValueChanged;

            // 居中
            _rbAlignCenter = new RadioButton
            {
                Text = "居中(&C)",
                Location = new Point(100, 25),
                Size = new Size(80, 20),
                Tag = "Center"
            };
            _rbAlignCenter.CheckedChanged += OnControlValueChanged;

            // 右对齐
            _rbAlignRight = new RadioButton
            {
                Text = "右对齐(&R)",
                Location = new Point(15, 50),
                Size = new Size(80, 20),
                Tag = "Right"
            };
            _rbAlignRight.CheckedChanged += OnControlValueChanged;

            // 两端对齐
            _rbAlignJustify = new RadioButton
            {
                Text = "两端对齐(&J)",
                Location = new Point(100, 50),
                Size = new Size(80, 20),
                Tag = "Justify"
            };
            _rbAlignJustify.CheckedChanged += OnControlValueChanged;

            // 分散对齐
            _rbAlignDistribute = new RadioButton
            {
                Text = "分散对齐(&D)",
                Location = new Point(15, 75),
                Size = new Size(80, 20),
                Tag = "Distribute"
            };
            _rbAlignDistribute.CheckedChanged += OnControlValueChanged;

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] { _rbAlignLeft, _rbAlignCenter, _rbAlignRight, _rbAlignJustify, _rbAlignDistribute });
            tabPageParagraph.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建段落缩进组
        /// </summary>
        private void CreateParagraphIndentGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "缩进",
                Location = new Point(240, 20),
                Size = new Size(280, 120),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 文本之前缩进
            var lblBefore = new Label
            {
                Text = "文本之前:",
                Location = new Point(15, 30),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _numIndentBefore = new NumericUpDown
            {
                Location = new Point(90, 28),
                Size = new Size(60, 23),
                DecimalPlaces = 1,
                Minimum = 0,
                Maximum = 100,
                Value = 0,
                TextAlign = HorizontalAlignment.Center
            };
            _numIndentBefore.ValueChanged += OnControlValueChanged;
            SetNumericUpDownTextAlign(_numIndentBefore);

            var lblBeforeUnit = new Label
            {
                Text = "厘米",
                Location = new Point(155, 30),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // 特殊缩进
            var lblSpecial = new Label
            {
                Text = "特殊缩进:",
                Location = new Point(15, 60),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbIndentSpecial = new ComboBox
            {
                Location = new Point(90, 58),
                Size = new Size(80, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _cmbIndentSpecial.Items.AddRange(new string[] { "无", "首行缩进", "悬挂缩进" });
            _cmbIndentSpecial.SelectedIndex = 0;
            _cmbIndentSpecial.SelectedIndexChanged += OnControlValueChanged;
            SetComboBoxTextAlign(_cmbIndentSpecial);

            _numIndentSpecial = new NumericUpDown
            {
                Location = new Point(180, 58),
                Size = new Size(60, 23),
                DecimalPlaces = 1,
                Minimum = 0,
                Maximum = 10,
                Value = 2,
                TextAlign = HorizontalAlignment.Center,
                Enabled = false
            };
            _numIndentSpecial.ValueChanged += OnControlValueChanged;
            SetNumericUpDownTextAlign(_numIndentSpecial);

            var lblSpecialUnit = new Label
            {
                Text = "字符",
                Location = new Point(245, 60),
                Size = new Size(30, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // 特殊缩进类型变更事件
            _cmbIndentSpecial.SelectedIndexChanged += (s, e) =>
            {
                if (_numIndentSpecial != null)
                    _numIndentSpecial.Enabled = _cmbIndentSpecial.SelectedIndex > 0;
            };

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] {
                lblBefore, _numIndentBefore, lblBeforeUnit,
                lblSpecial, _cmbIndentSpecial, _numIndentSpecial, lblSpecialUnit
            });
            tabPageParagraph.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建段落间距组
        /// </summary>
        private void CreateParagraphSpacingGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "间距",
                Location = new Point(540, 20),
                Size = new Size(280, 120),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 段前间距
            var lblBefore = new Label
            {
                Text = "段前:",
                Location = new Point(15, 30),
                Size = new Size(50, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _numSpacingBefore = new NumericUpDown
            {
                Location = new Point(50, 28),
                Size = new Size(60, 23),
                DecimalPlaces = 1,
                Minimum = 0,
                Maximum = 100,
                Value = 0,
                TextAlign = HorizontalAlignment.Center
            };
            _numSpacingBefore.ValueChanged += OnControlValueChanged;
            SetNumericUpDownTextAlign(_numSpacingBefore);

            var lblBeforeUnit = new Label
            {
                Text = "磅",
                Location = new Point(115, 30),
                Size = new Size(20, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // 段后间距
            var lblAfter = new Label
            {
                Text = "段后:",
                Location = new Point(135, 30),
                Size = new Size(50, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _numSpacingAfter = new NumericUpDown
            {
                Location = new Point(180, 28),
                Size = new Size(60, 23),
                DecimalPlaces = 1,
                Minimum = 0,
                Maximum = 100,
                Value = 0,
                TextAlign = HorizontalAlignment.Center
            };
            _numSpacingAfter.ValueChanged += OnControlValueChanged;
            SetNumericUpDownTextAlign(_numSpacingAfter);

            var lblAfterUnit = new Label
            {
                Text = "磅",
                Location = new Point(240, 30),
                Size = new Size(20, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // 行距
            var lblLineSpacing = new Label
            {
                Text = "行距:",
                Location = new Point(15, 65),
                Size = new Size(50, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbLineSpacing = new ComboBox
            {
                Location = new Point(70, 63),
                Size = new Size(100, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _cmbLineSpacing.Items.AddRange(new string[] { "单倍行距", "1.5倍行距", "2倍行距", "多倍行距", "固定值" });
            _cmbLineSpacing.SelectedIndex = 0;
            _cmbLineSpacing.SelectedIndexChanged += OnControlValueChanged;
            SetComboBoxTextAlign(_cmbLineSpacing);

            _numLineSpacing = new NumericUpDown
            {
                Location = new Point(180, 63),
                Size = new Size(60, 23),
                DecimalPlaces = 1,
                Minimum = 0.1m,
                Maximum = 10,
                Value = 1,
                TextAlign = HorizontalAlignment.Center,
                Enabled = false
            };
            _numLineSpacing.ValueChanged += OnControlValueChanged;
            SetNumericUpDownTextAlign(_numLineSpacing);

            // 行距类型变更事件
            _cmbLineSpacing.SelectedIndexChanged += (s, e) =>
            {
                if (_numLineSpacing != null)
                    _numLineSpacing.Enabled = _cmbLineSpacing.SelectedIndex >= 3;
            };

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] {
                lblBefore, _numSpacingBefore, lblBeforeUnit,
                lblAfter, _numSpacingAfter, lblAfterUnit,
                lblLineSpacing, _cmbLineSpacing, _numLineSpacing
            });
            tabPageParagraph.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建段落选项组
        /// </summary>
        private void CreateParagraphOptionsGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "中文排版选项",
                Location = new Point(20, 160),
                Size = new Size(400, 100),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 按中文习惯控制中文首尾字符
            _chkChineseControl = new CheckBox
            {
                Text = "按中文习惯控制中文首尾字符(&H)",
                Location = new Point(15, 25),
                Size = new Size(250, 20),
                Checked = true
            };
            _chkChineseControl.CheckedChanged += OnControlValueChanged;

            // 允许西文在单词中间换行
            _chkWordWrap = new CheckBox
            {
                Text = "允许西文在单词中间换行(&W)",
                Location = new Point(15, 50),
                Size = new Size(250, 20),
                Checked = false
            };
            _chkWordWrap.CheckedChanged += OnControlValueChanged;

            // 允许标点移除边界
            _chkPunctuationBoundary = new CheckBox
            {
                Text = "允许标点移除边界(&P)",
                Location = new Point(15, 75),
                Size = new Size(250, 20),
                Checked = false
            };
            _chkPunctuationBoundary.CheckedChanged += OnControlValueChanged;

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] { _chkChineseControl, _chkWordWrap, _chkPunctuationBoundary });
            tabPageParagraph.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建文本对齐方式组
        /// </summary>
        private void CreateTextAlignmentGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "文本对齐方式",
                Location = new Point(440, 160),
                Size = new Size(200, 100),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 自动
            _rbTextAlignAuto = new RadioButton
            {
                Text = "自动(&A)",
                Location = new Point(15, 25),
                Size = new Size(80, 20),
                Checked = true,
                Tag = "Auto"
            };
            _rbTextAlignAuto.CheckedChanged += OnControlValueChanged;

            // 居中
            _rbTextAlignCenter = new RadioButton
            {
                Text = "居中(&C)",
                Location = new Point(100, 25),
                Size = new Size(80, 20),
                Tag = "Center"
            };
            _rbTextAlignCenter.CheckedChanged += OnControlValueChanged;

            // 基线
            _rbTextAlignBaseline = new RadioButton
            {
                Text = "基线(&B)",
                Location = new Point(15, 50),
                Size = new Size(80, 20),
                Tag = "Baseline"
            };
            _rbTextAlignBaseline.CheckedChanged += OnControlValueChanged;

            // 底部
            _rbTextAlignBottom = new RadioButton
            {
                Text = "底部(&T)",
                Location = new Point(100, 50),
                Size = new Size(80, 20),
                Tag = "Bottom"
            };
            _rbTextAlignBottom.CheckedChanged += OnControlValueChanged;

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] { _rbTextAlignAuto, _rbTextAlignCenter, _rbTextAlignBaseline, _rbTextAlignBottom });
            tabPageParagraph.Controls.Add(groupBox);
        }

        #endregion

        #region 字体格式设置控件创建方法

        /// <summary>
        /// 创建字体选择组
        /// </summary>
        private void CreateFontSelectionGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "字体选择",
                Location = new Point(20, 20),
                Size = new Size(400, 120),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 中文字体
            var lblChineseFont = new Label
            {
                Text = "中文字体:",
                Location = new Point(15, 30),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbChineseFont = new ComboBox
            {
                Location = new Point(90, 28),
                Size = new Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            // 添加常用中文字体
            _cmbChineseFont.Items.AddRange(new string[] {
                "宋体", "黑体", "楷体", "仿宋", "微软雅黑", "华文宋体", "华文黑体", "华文楷体", "方正舒体", "方正姚体"
            });
            _cmbChineseFont.SelectedIndex = 0;
            _cmbChineseFont.SelectedIndexChanged += OnControlValueChanged;
            SetComboBoxTextAlign(_cmbChineseFont);

            // 西文字体
            var lblEnglishFont = new Label
            {
                Text = "西文字体:",
                Location = new Point(15, 60),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbEnglishFont = new ComboBox
            {
                Location = new Point(90, 58),
                Size = new Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            // 添加常用西文字体
            _cmbEnglishFont.Items.AddRange(new string[] {
                "Arial", "Times New Roman", "Calibri", "Verdana", "Tahoma", "Georgia", "Comic Sans MS", "Impact"
            });
            _cmbEnglishFont.SelectedIndex = 0;
            _cmbEnglishFont.SelectedIndexChanged += OnControlValueChanged;
            SetComboBoxTextAlign(_cmbEnglishFont);

            // 字体大小
            var lblFontSize = new Label
            {
                Text = "字体大小:",
                Location = new Point(260, 30),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _numFontSize = new NumericUpDown
            {
                Location = new Point(330, 28),
                Size = new Size(60, 23),
                Minimum = 8,
                Maximum = 72,
                Value = 12,
                TextAlign = HorizontalAlignment.Center
            };
            _numFontSize.ValueChanged += OnControlValueChanged;
            SetNumericUpDownTextAlign(_numFontSize);

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] {
                lblChineseFont, _cmbChineseFont,
                lblEnglishFont, _cmbEnglishFont,
                lblFontSize, _numFontSize
            });
            tabPageFont.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建字体样式组
        /// </summary>
        private void CreateFontStyleGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "字体样式",
                Location = new Point(440, 20),
                Size = new Size(380, 120),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 字体样式
            var lblFontStyle = new Label
            {
                Text = "字体样式:",
                Location = new Point(15, 30),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbFontStyle = new ComboBox
            {
                Location = new Point(90, 28),
                Size = new Size(100, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _cmbFontStyle.Items.AddRange(new string[] { "常规", "倾斜", "加粗", "倾斜加粗" });
            _cmbFontStyle.SelectedIndex = 0;
            _cmbFontStyle.SelectedIndexChanged += OnControlValueChanged;
            SetComboBoxTextAlign(_cmbFontStyle);

            // 字体颜色
            var lblFontColor = new Label
            {
                Text = "字体颜色:",
                Location = new Point(200, 30),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _btnFontColor = new Button
            {
                Location = new Point(275, 28),
                Size = new Size(80, 23),
                Text = "选择颜色",
                BackColor = _fontColor,
                ForeColor = GetContrastColor(_fontColor)
            };
            _btnFontColor.Click += BtnFontColor_Click;

            // 下划线线型
            var lblUnderline = new Label
            {
                Text = "下划线:",
                Location = new Point(15, 60),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _cmbUnderline = new ComboBox
            {
                Location = new Point(90, 58),
                Size = new Size(100, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _cmbUnderline.Items.AddRange(new string[] { "无", "单下划线", "双下划线", "粗下划线", "点下划线", "虚线下划线" });
            _cmbUnderline.SelectedIndex = 0;
            _cmbUnderline.SelectedIndexChanged += OnControlValueChanged;
            SetComboBoxTextAlign(_cmbUnderline);

            // 下划线颜色
            var lblUnderlineColor = new Label
            {
                Text = "下划线颜色:",
                Location = new Point(200, 60),
                Size = new Size(70, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            _btnUnderlineColor = new Button
            {
                Location = new Point(275, 58),
                Size = new Size(80, 23),
                Text = "选择颜色",
                BackColor = _underlineColor,
                ForeColor = GetContrastColor(_underlineColor),
                Enabled = false
            };
            _btnUnderlineColor.Click += BtnUnderlineColor_Click;

            // 下划线类型变更事件
            _cmbUnderline.SelectedIndexChanged += (s, e) =>
            {
                if (_btnUnderlineColor != null)
                    _btnUnderlineColor.Enabled = _cmbUnderline.SelectedIndex > 0;
            };

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] {
                lblFontStyle, _cmbFontStyle,
                lblFontColor, _btnFontColor,
                lblUnderline, _cmbUnderline,
                lblUnderlineColor, _btnUnderlineColor
            });
            tabPageFont.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建字体效果组
        /// </summary>
        private void CreateFontEffectsGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "文字效果",
                Location = new Point(20, 160),
                Size = new Size(400, 100),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 删除线
            _chkStrikethrough = new CheckBox
            {
                Text = "删除线(&S)",
                Location = new Point(15, 25),
                Size = new Size(80, 20),
                Checked = false
            };
            _chkStrikethrough.CheckedChanged += OnControlValueChanged;

            // 双删除线
            _chkDoubleStrikethrough = new CheckBox
            {
                Text = "双删除线(&D)",
                Location = new Point(100, 25),
                Size = new Size(100, 20),
                Checked = false
            };
            _chkDoubleStrikethrough.CheckedChanged += OnControlValueChanged;

            // 上标
            _chkSuperscript = new CheckBox
            {
                Text = "上标(&U)",
                Location = new Point(210, 25),
                Size = new Size(80, 20),
                Checked = false
            };
            _chkSuperscript.CheckedChanged += OnControlValueChanged;

            // 下标
            _chkSubscript = new CheckBox
            {
                Text = "下标(&B)",
                Location = new Point(300, 25),
                Size = new Size(80, 20),
                Checked = false
            };
            _chkSubscript.CheckedChanged += OnControlValueChanged;

            // 上标和下标互斥
            _chkSuperscript.CheckedChanged += (s, e) =>
            {
                if (_chkSuperscript != null && _chkSubscript != null && _chkSuperscript.Checked)
                    _chkSubscript.Checked = false;
            };

            _chkSubscript.CheckedChanged += (s, e) =>
            {
                if (_chkSuperscript != null && _chkSubscript != null && _chkSubscript.Checked)
                    _chkSuperscript.Checked = false;
            };

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] {
                _chkStrikethrough, _chkDoubleStrikethrough, _chkSuperscript, _chkSubscript
            });
            tabPageFont.Controls.Add(groupBox);
        }

        #endregion

        #region 主题设置控件创建方法

        /// <summary>
        /// 创建内置主题组
        /// </summary>
        private void CreateBuiltInThemeGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "应用内置主题",
                Location = new Point(20, 20),
                Size = new Size(400, 180),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 内置主题列表
            _listThemes = new ListBox
            {
                Location = new Point(15, 25),
                Size = new Size(280, 120),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 添加常用内置主题
            _listThemes.Items.AddRange(new string[] {
                "Office 主题",
                "Facet 主题",
                "Ion 主题",
                "Retrospect 主题",
                "Slice 主题",
                "Wisp 主题",
                "Berlin 主题",
                "Celestial 主题",
                "Dividend 主题",
                "Droplet 主题"
            });
            _listThemes.SelectedIndex = 0;
            _listThemes.SelectedIndexChanged += OnControlValueChanged;

            // 应用主题按钮
            var btnApplyTheme = new Button
            {
                Text = "应用主题(&A)",
                Location = new Point(305, 25),
                Size = new Size(80, 30),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
            btnApplyTheme.Click += BtnApplyTheme_Click;

            // 预览主题按钮
            var btnPreviewTheme = new Button
            {
                Text = "预览主题(&P)",
                Location = new Point(305, 65),
                Size = new Size(80, 30),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
            btnPreviewTheme.Click += BtnPreviewTheme_Click;

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] { _listThemes, btnApplyTheme, btnPreviewTheme });
            tabPageTheme.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建自定义主题组
        /// </summary>
        private void CreateCustomThemeGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "自定义主题",
                Location = new Point(440, 20),
                Size = new Size(380, 180),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 创建新主题
            var btnCreateTheme = new Button
            {
                Text = "创建新主题(&N)",
                Location = new Point(15, 30),
                Size = new Size(100, 30),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 编辑当前主题
            var btnEditTheme = new Button
            {
                Text = "编辑当前主题(&E)",
                Location = new Point(125, 30),
                Size = new Size(100, 30),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 保存主题
            var btnSaveTheme = new Button
            {
                Text = "保存主题(&S)",
                Location = new Point(235, 30),
                Size = new Size(100, 30),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 导入主题
            var btnImportTheme = new Button
            {
                Text = "导入主题(&I)",
                Location = new Point(15, 70),
                Size = new Size(100, 30),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 导出主题
            var btnExportTheme = new Button
            {
                Text = "导出主题(&X)",
                Location = new Point(125, 70),
                Size = new Size(100, 30),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 删除主题
            var btnDeleteTheme = new Button
            {
                Text = "删除主题(&D)",
                Location = new Point(235, 70),
                Size = new Size(100, 30),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] {
                btnCreateTheme, btnEditTheme, btnSaveTheme,
                btnImportTheme, btnExportTheme, btnDeleteTheme
            });
            tabPageTheme.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建主题颜色方案组
        /// </summary>
        private void CreateThemeColorSchemeGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "主题颜色方案",
                Location = new Point(20, 220),
                Size = new Size(400, 120),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 颜色方案列表
            _cmbColorScheme = new ComboBox
            {
                Location = new Point(15, 30),
                Size = new Size(200, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
            SetComboBoxTextAlign(_cmbColorScheme);

            // 添加常用颜色方案
            _cmbColorScheme.Items.AddRange(new string[] {
                "Office 颜色方案",
                "蓝色方案",
                "绿色方案",
                "橙色方案",
                "红色方案",
                "紫色方案",
                "灰色方案",
                "彩虹方案"
            });
            _cmbColorScheme.SelectedIndex = 0;
            _cmbColorScheme.SelectedIndexChanged += OnControlValueChanged;

            // 应用颜色方案按钮
            var btnApplyColorScheme = new Button
            {
                Text = "应用方案(&A)",
                Location = new Point(230, 28),
                Size = new Size(80, 27),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
            btnApplyColorScheme.Click += BtnApplyColorScheme_Click;

            // 自定义颜色方案按钮
            var btnCustomColorScheme = new Button
            {
                Text = "自定义方案(&C)",
                Location = new Point(320, 28),
                Size = new Size(80, 27),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
            btnCustomColorScheme.Click += BtnCustomColorScheme_Click;

            // 颜色预览面板
            _panelColorPreview = new Panel
            {
                Location = new Point(15, 65),
                Size = new Size(370, 40),
                BorderStyle = BorderStyle.FixedSingle
            };

            // 添加颜色预览块
            for (int i = 0; i < 8; i++)
            {
                var colorBlock = new Panel
                {
                    Location = new Point(i * 45 + 5, 5),
                    Size = new Size(40, 30),
                    BorderStyle = BorderStyle.FixedSingle,
                    BackColor = GetPreviewColor(i)
                };
                _panelColorPreview.Controls.Add(colorBlock);
            }

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] {
                _cmbColorScheme, btnApplyColorScheme, btnCustomColorScheme, _panelColorPreview
            });
            tabPageTheme.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建主题字体方案组
        /// </summary>
        private void CreateThemeFontSchemeGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "主题字体方案",
                Location = new Point(440, 220),
                Size = new Size(380, 120),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 字体方案列表
            _cmbFontScheme = new ComboBox
            {
                Location = new Point(15, 30),
                Size = new Size(160, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
            SetComboBoxTextAlign(_cmbFontScheme);

            // 添加常用字体方案
            _cmbFontScheme.Items.AddRange(new string[] {
                "Office 字体方案",
                "经典字体方案",
                "现代字体方案",
                "优雅字体方案",
                "简洁字体方案",
                "艺术字体方案"
            });
            _cmbFontScheme.SelectedIndex = 0;
            _cmbFontScheme.SelectedIndexChanged += OnControlValueChanged;

            // 应用字体方案按钮
            var btnApplyFontScheme = new Button
            {
                Text = "应用方案(&A)",
                Location = new Point(185, 28),
                Size = new Size(80, 27),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
            btnApplyFontScheme.Click += BtnApplyFontScheme_Click;

            // 自定义字体方案按钮
            var btnCustomFontScheme = new Button
            {
                Text = "自定义方案(&C)",
                Location = new Point(280, 28),
                Size = new Size(80, 27),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
            btnCustomFontScheme.Click += BtnCustomFontScheme_Click;

            // 字体预览标签
            _lblFontPreview = new Label
            {
                Text = "标题字体: 微软雅黑\n正文字体: 宋体",
                Location = new Point(15, 65),
                Size = new Size(350, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular),
                BorderStyle = BorderStyle.FixedSingle,
                TextAlign = ContentAlignment.MiddleLeft
            };

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] {
                _cmbFontScheme, btnApplyFontScheme, btnCustomFontScheme, _lblFontPreview
            });
            tabPageTheme.Controls.Add(groupBox);
        }

        /// <summary>
        /// 获取预览颜色
        /// </summary>
        /// <param name="index">颜色索引</param>
        /// <returns>颜色</returns>
        private static Color GetPreviewColor(int index)
        {
            Color[] colors = {
                Color.FromArgb(68, 114, 196),   // 蓝色
                Color.FromArgb(237, 125, 49),   // 橙色
                Color.FromArgb(165, 165, 165),  // 灰色
                Color.FromArgb(255, 192, 0),    // 黄色
                Color.FromArgb(91, 155, 213),   // 浅蓝色
                Color.FromArgb(112, 173, 71),   // 绿色
                Color.FromArgb(158, 72, 14),    // 棕色
                Color.FromArgb(99, 99, 99)      // 深灰色
            };
            return index < colors.Length ? colors[index] : Color.Gray;
        }

        #endregion

        #region 母版、布局和样式设置控件创建方法

        /// <summary>
        /// 创建母版幻灯片组
        /// </summary>
        private void CreateMasterSlideGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "母版设置",
                Location = new Point(20, 20),
                Size = new Size(800, 300),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 幻灯片母版
            _btnSlideMaster = new Button
            {
                Text = "幻灯片母版(&S)",
                Location = new Point(20, 30),
                Size = new Size(120, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
            _btnSlideMaster.Click += BtnSlideMaster_Click;

            // 标题母版
            _btnTitleMaster = new Button
            {
                Text = "标题母版(&T)",
                Location = new Point(160, 30),
                Size = new Size(120, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
            _btnTitleMaster.Click += BtnTitleMaster_Click;

            // 备注母版
            _btnNotesMaster = new Button
            {
                Text = "备注母版(&N)",
                Location = new Point(300, 30),
                Size = new Size(120, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
            _btnNotesMaster.Click += BtnNotesMaster_Click;

            // 讲义母版
            _btnHandoutMaster = new Button
            {
                Text = "讲义母版(&H)",
                Location = new Point(440, 30),
                Size = new Size(120, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
            _btnHandoutMaster.Click += BtnHandoutMaster_Click;

            var lblInfo = new Label
            {
                Text = "母版设置功能允许您定义演示文稿的整体外观和格式。\n" +
                       "• 幻灯片母版：控制普通幻灯片的布局和格式\n" +
                       "• 标题母版：控制标题幻灯片的布局和格式\n" +
                       "• 备注母版：控制备注页面的布局和格式\n" +
                       "• 讲义母版：控制打印讲义的布局和格式",
                Location = new Point(20, 90),
                Size = new Size(760, 180),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] {
                _btnSlideMaster, _btnTitleMaster, _btnNotesMaster, _btnHandoutMaster, lblInfo
            });
            tabPageMaster.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建幻灯片布局组
        /// </summary>
        private void CreateSlideLayoutGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "幻灯片布局",
                Location = new Point(20, 20),
                Size = new Size(800, 300),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 标题幻灯片布局
            _btnTitleLayout = new Button
            {
                Text = "标题幻灯片布局(&T)",
                Location = new Point(20, 30),
                Size = new Size(140, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
            _btnTitleLayout.Click += BtnTitleLayout_Click;

            // 内容布局
            _btnContentLayout = new Button
            {
                Text = "内容布局(&C)",
                Location = new Point(180, 30),
                Size = new Size(140, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
            _btnContentLayout.Click += BtnContentLayout_Click;

            // 两栏布局
            _btnTwoColumnLayout = new Button
            {
                Text = "两栏布局(&W)",
                Location = new Point(340, 30),
                Size = new Size(140, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
            _btnTwoColumnLayout.Click += BtnTwoColumnLayout_Click;

            // 图片布局
            _btnPictureLayout = new Button
            {
                Text = "图片布局(&P)",
                Location = new Point(500, 30),
                Size = new Size(140, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
            _btnPictureLayout.Click += BtnPictureLayout_Click;

            // 自定义布局
            _btnCustomLayout = new Button
            {
                Text = "自定义布局(&U)",
                Location = new Point(660, 30),
                Size = new Size(140, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
            _btnCustomLayout.Click += BtnCustomLayout_Click;

            var lblInfo = new Label
            {
                Text = "布局设置功能允许您选择和自定义幻灯片的内容排列方式。\n" +
                       "• 标题幻灯片布局：用于演示文稿的开头和章节标题\n" +
                       "• 内容布局：包含标题和内容占位符的标准布局\n" +
                       "• 两栏布局：将内容分为两列显示\n" +
                       "• 图片布局：专门用于展示图片的布局\n" +
                       "• 自定义布局：根据需要创建特殊的布局样式",
                Location = new Point(20, 90),
                Size = new Size(760, 180),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] {
                _btnTitleLayout, _btnContentLayout, _btnTwoColumnLayout, _btnPictureLayout, _btnCustomLayout, lblInfo
            });
            tabPageLayout.Controls.Add(groupBox);
        }

        /// <summary>
        /// 创建样式组
        /// </summary>
        private void CreateStyleGroup()
        {
            var groupBox = new GroupBox
            {
                Text = "样式设置",
                Location = new Point(20, 20),
                Size = new Size(800, 300),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            // 形状样式
            _btnShapeStyle = new Button
            {
                Text = "形状样式(&S)",
                Location = new Point(20, 30),
                Size = new Size(140, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
            _btnShapeStyle.Click += BtnShapeStyle_Click;

            // 文本样式
            _btnTextStyle = new Button
            {
                Text = "文本样式(&T)",
                Location = new Point(180, 30),
                Size = new Size(140, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
            _btnTextStyle.Click += BtnTextStyle_Click;

            // 表格样式
            _btnTableStyle = new Button
            {
                Text = "表格样式(&B)",
                Location = new Point(340, 30),
                Size = new Size(140, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
            _btnTableStyle.Click += BtnTableStyle_Click;

            // 图表样式
            _btnChartStyle = new Button
            {
                Text = "图表样式(&H)",
                Location = new Point(500, 30),
                Size = new Size(140, 40),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };
            _btnChartStyle.Click += BtnChartStyle_Click;

            var lblInfo = new Label
            {
                Text = "样式设置功能允许您定义各种元素的外观样式。\n" +
                       "• 形状样式：设置形状的填充、边框、效果等样式\n" +
                       "• 文本样式：设置文本的字体、颜色、格式等样式\n" +
                       "• 表格样式：设置表格的边框、填充、字体等样式\n" +
                       "• 图表样式：设置图表的颜色、字体、效果等样式",
                Location = new Point(20, 90),
                Size = new Size(760, 180),
                Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular)
            };

            groupBox.Controls.AddRange(new System.Windows.Forms.Control[] {
                _btnShapeStyle, _btnTextStyle, _btnTableStyle, _btnChartStyle, lblInfo
            });
            tabPageStyle.Controls.Add(groupBox);
        }

        #endregion
    }

    #region 格式设置数据类

    /// <summary>
    /// 段落格式设置
    /// </summary>
    public class ParagraphFormatSettings
    {
        /// <summary>
        /// 对齐方式
        /// </summary>
        public TextAlignment Alignment { get; set; } = TextAlignment.Left;

        /// <summary>
        /// 文本之前缩进（厘米）
        /// </summary>
        public float IndentBefore { get; set; } = 0;

        /// <summary>
        /// 特殊缩进类型
        /// </summary>
        public string SpecialIndentType { get; set; } = "无";

        /// <summary>
        /// 特殊缩进值（字符）
        /// </summary>
        public float SpecialIndentValue { get; set; } = 2;

        /// <summary>
        /// 段前间距（磅）
        /// </summary>
        public float SpacingBefore { get; set; } = 0;

        /// <summary>
        /// 段后间距（磅）
        /// </summary>
        public float SpacingAfter { get; set; } = 0;

        /// <summary>
        /// 行距类型
        /// </summary>
        public string LineSpacingType { get; set; } = "单倍行距";

        /// <summary>
        /// 行距值
        /// </summary>
        public float LineSpacingValue { get; set; } = 1;

        /// <summary>
        /// 按中文习惯控制中文首尾字符
        /// </summary>
        public bool ChineseControl { get; set; } = true;

        /// <summary>
        /// 允许西文在单词中间换行
        /// </summary>
        public bool WordWrap { get; set; } = false;

        /// <summary>
        /// 允许标点移除边界
        /// </summary>
        public bool PunctuationBoundary { get; set; } = false;

        /// <summary>
        /// 文本对齐方式
        /// </summary>
        public string TextAlignmentType { get; set; } = "自动";
    }

    /// <summary>
    /// 字体格式设置
    /// </summary>
    public class FontFormatSettings
    {
        /// <summary>
        /// 中文字体
        /// </summary>
        public string ChineseFont { get; set; } = "宋体";

        /// <summary>
        /// 西文字体
        /// </summary>
        public string EnglishFont { get; set; } = "Arial";

        /// <summary>
        /// 字体样式
        /// </summary>
        public string FontStyle { get; set; } = "常规";

        /// <summary>
        /// 字体大小
        /// </summary>
        public float FontSize { get; set; } = 12;

        /// <summary>
        /// 字体颜色
        /// </summary>
        public Color FontColor { get; set; } = Color.Black;

        /// <summary>
        /// 下划线类型
        /// </summary>
        public string UnderlineType { get; set; } = "无";

        /// <summary>
        /// 下划线颜色
        /// </summary>
        public Color UnderlineColor { get; set; } = Color.Black;

        /// <summary>
        /// 删除线
        /// </summary>
        public bool Strikethrough { get; set; } = false;

        /// <summary>
        /// 双删除线
        /// </summary>
        public bool DoubleStrikethrough { get; set; } = false;

        /// <summary>
        /// 上标
        /// </summary>
        public bool Superscript { get; set; } = false;

        /// <summary>
        /// 下标
        /// </summary>
        public bool Subscript { get; set; } = false;
    }

    /// <summary>
    /// 主题设置
    /// </summary>
    public class ThemeSettings
    {
        /// <summary>
        /// 选中的主题名称
        /// </summary>
        public string SelectedTheme { get; set; } = "Office 主题";

        /// <summary>
        /// 选中的颜色方案
        /// </summary>
        public string SelectedColorScheme { get; set; } = "Office 颜色方案";

        /// <summary>
        /// 选中的字体方案
        /// </summary>
        public string SelectedFontScheme { get; set; } = "Office 字体方案";
    }

    #endregion
}
